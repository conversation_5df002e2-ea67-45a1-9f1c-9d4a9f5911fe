@extends('layouts.app')

@section('content')
<div class="container">
  @if (session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      {{ session('success') }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
  @if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      {{ session('error') }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
<div class="d-flex justify-content-between align-items-center mb-3">
  <h3>
    <i class="fas fa-users me-2"></i>
    Employees
  </h3>
  <div>
    <button id="TrashView" class="btn btn-outline-secondary me-2">
      <i class="fas fa-trash me-1"></i>
      <span id="TrashName">View Trashed</span>
    </button>
    <button id="addEmployeeBtn" class="btn btn-primary">
      <i class="fas fa-plus me-1"></i>
      Add Employee
    </button>
  </div>
</div>

<div class="mb-3 row">
  <div class="col-md-4">
    <input id="employeeSearch" type="text" class="form-control" placeholder="Search name, email, phone">
  </div>
  <div class="col-md-4">
    <select id="departmentFilter" class="form-select">
      <option value="">All Departments</option>
      @foreach(\App\Models\Department::where('status','active')->get() as $d)
        <option value="{{ $d->id }}">{{ $d->name }} ({{ $d->code }})</option>
      @endforeach
    </select>
  </div>
  <div class="col-md-4">
    <button id="applyFilter" class="btn btn-secondary">Apply</button>
  </div>
</div>

<div id="employeesLists"></div>
<div id="employeeCreateEdit"></div>
<div id="deleteConfirmPopup"></div>
<div>
@endsection


@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
  const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
  let isTrash = false;

  function getStoreUrl() {
    return isTrash ? '{{ route('employees.trashed.data') }}' : '{{ route('employees.data') }}';
  }

  const store = new DevExpress.data.CustomStore({
    key: 'id',
    load: function(loadOptions) {
      const params = new URLSearchParams();
      if (loadOptions.skip) params.append('skip', loadOptions.skip);
      if (loadOptions.take) params.append('take', loadOptions.take);
      if (document.getElementById('employeeSearch').value) {
        params.append('search', document.getElementById('employeeSearch').value);
      }
      if (document.getElementById('departmentFilter').value) {
        params.append('department_id', document.getElementById('departmentFilter').value);
      }
      return fetch(getStoreUrl() + '?' + params, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': csrfToken,
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => response.json())
      .then(json => {
        if (json.error) throw new Error(json.message || 'Unknown error');
        return { data: json.data, totalCount: json.totalCount };
      })
      .catch(error => {
        alert('Error loading employees: ' + error.message);
        return { data: [], totalCount: 0 };
      });
    }
  });

  const grid = new DevExpress.ui.dxDataGrid(document.getElementById('employeesLists'), {
    dataSource: store,
    remoteOperations: { paging: true, sorting: true, filtering: true },
    paging: { pageSize: 10 },
    showBorders: true,
    rowAlternationEnabled: true,
    columns: [
      { dataField: 'id', caption: 'ID', width: 70 },
      { dataField: 'emp_id', caption: 'Employee ID' },
      { dataField: 'name', caption: 'Employee Name' },
      { dataField: 'email', caption: 'Email' },
      { dataField: 'phone', caption: 'Phone', width: 120 },
      { dataField: 'department_name', caption: 'Department' },
      { dataField: 'designation', caption: 'Designation' },
      {
        dataField: 'status',
        caption: 'Status',
        width: 100,
        cellTemplate: function(container, options) {
          const status = options.value;
          const badgeClass = status === 'active' ? 'badge bg-success' : 'badge bg-danger';
          container.innerHTML = `<span class="${badgeClass}">${status}</span>`;
        }
      },
      {
        caption: 'Actions',
        width: 180,
        allowSorting: false,
        cellTemplate: function(container, options) {
          const id = options.data.id;
          let btns = '';
          if (!isTrash) {
            btns += `<button class='btn btn-sm btn-outline-primary me-1' onclick='editEmployee(${id})'><i class="fas fa-edit"></i></button>`;
            btns += `<button class='btn btn-sm btn-outline-danger' onclick='confirmDelete(${id})'><i class="fas fa-trash"></i></button>`;
          } else {
            btns += `<button class='btn btn-sm btn-outline-success' onclick='restoreEmployee(${id})'><i class="fas fa-undo"></i> Restore</button>`;
          }
          container.innerHTML = btns;
        }
      }
    ],
    onRowPrepared: function(e) {
      if (e.rowType === 'data' && e.data.status === 'inactive') {
        e.rowElement.style.opacity = '0.6';
      }
    }
  });

  window.editEmployee = function(id) {
    showEmployeeCreateEdit('edit', id);
  };
  window.confirmDelete = function(id) {
    showDeletePopup(id);
  };
  window.restoreEmployee = function(id) {
    fetch(`/employees/${id}/restore`, {
      method: 'POST',
      headers: { 'X-CSRF-TOKEN': csrfToken, 'X-Requested-With': 'XMLHttpRequest' }
    }).then(() => grid.refresh());
  };

  document.getElementById('addEmployeeBtn').addEventListener('click', function() {
    showEmployeeCreateEdit('add');
  });
  document.getElementById('TrashView').addEventListener('click', function() {
    isTrash = !isTrash;
    document.getElementById('TrashName').innerText = isTrash ? 'View Active' : 'View Trashed';
    grid.refresh();
  });
  document.getElementById('applyFilter').addEventListener('click', function(){
    grid.getDataSource().reload();
  });
  document.getElementById('employeeSearch').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      grid.getDataSource().reload();
    }
  });

  function showEmployeeCreateEdit(mode, id = null) {
  let title = mode === 'add' ? 'Add Employee' : 'Edit Employee';
  let url = mode === 'add' ? '/employees' : `/employees/${id}`;
  let method = 'POST';
  let employee = { name: '', email: '', phone: '', department_id: '', designation: '', status: 'active', photo: '', resume: '' };

    if (mode === 'edit') {
      fetch(`/employees/${id}/editdata`, { headers: { 'X-Requested-With': 'XMLHttpRequest' } })
        .then(res => res.json())
        .then(data => {
          employee = data;
          openPopup();
        });
    } else {
      openPopup();
    }

    function openPopup() {
      const popup = new DevExpress.ui.dxPopup(document.getElementById('employeeCreateEdit'), {
        title: title,
        visible: true,
        width: 1000,
        height: 700,
        contentTemplate: function(contentElement) {
          contentElement.innerHTML = `
            <style>
              .toggle-switch { position: relative; display: inline-block; width: 60px; height: 32px; vertical-align: middle; }
              .toggle-switch input[type="checkbox"] { opacity: 0; width: 0; height: 0; }
              .slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #dc3545; transition: .4s; border-radius: 32px; }
              .slider:before { position: absolute; content: ""; height: 24px; width: 24px; left: 4px; bottom: 4px; background-color: white; transition: .4s; border-radius: 50%; }
              input[type="checkbox"]:checked + .slider { background-color: #198754; }
              input[type="checkbox"]:checked + .slider:before { transform: translateX(28px); }
              .toggle-label { margin-left: 12px; font-weight: bold; vertical-align: middle; color: #dc3545; transition: color .4s; cursor: pointer; }
              .toggle-switch input[type="checkbox"]:checked ~ .toggle-label { color: #198754; }
            </style>
            <form id="employeeForm" enctype="multipart/form-data" autoComplete="off">
              ${mode === 'edit' ? '<input type="hidden" name="_method" value="PUT" />' : ''}
              <div class="mb-3">
                <label class="form-label">Full Name</label>
                <input type="text" class="form-control" placeholder="Name" name="name" id="empName" value="${employee.name ? String(employee.name).replace(/"/g, '&quot;') : ''}" />
                <div id="nameError" class="text-danger small mt-1" style="display:none;"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Email</label>
                <input type="text" class="form-control" name="email" placeholder="Email" value="${employee.email ? String(employee.email).replace(/"/g, '&quot;') : ''}" />
              </div>
              <div class="mb-3">
                <label class="form-label">Phone</label>
                <input type="text" class="form-control" name="phone" id="empPhone" placeholder="Phone" value="${employee.phone ? String(employee.phone).replace(/"/g, '&quot;') : ''}" maxlength="10" minlength="10" />
                <div id="phoneError" class="text-danger small mt-1" style="display:none;"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Department</label>
                <select class="form-select" name="department_id" id="employeeDepartmentSelect">
                  <option value="">Select Department</option>
                  @foreach(\App\Models\Department::where('status','active')->get() as $d)
                    <option value="{{ $d->id }}" ${employee.department_id == {{ $d->id }} ? 'selected' : ''}>{{ $d->name }} ({{ $d->code }})</option>
                  @endforeach
                </select>
                <div id="deptError" class="text-danger small mt-1" style="display:none;"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Designation</label>
                <input type="text" class="form-control" name="designation" id="empDesignation" placeholder="Designation" value="${employee.designation ? String(employee.designation).replace(/"/g, '&quot;') : ''}" />
                <div id="designationError" class="text-danger small mt-1" style="display:none;"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Photo</label>
                <input type="file" class="form-control" name="photo" id="empPhoto" accept="image/*" />
                <div id="photoError" class="text-danger small mt-1" style="display:none;"></div>
                ${employee.photo ? `<div class='mt-2'><img src='${employee.photo}' alt='Photo' style='max-width:100px;max-height:100px;'/></div>` : ''}
              </div>
              <div class="mb-3">
                <label class="form-label">Resume</label>
                <input type="file" class="form-control" name="resume" id="empResume" accept="application/pdf,.doc,.docx" />
                <div id="resumeError" class="text-danger small mt-1" style="display:none;"></div>
                ${employee.resume ? `<div class='mt-2'><a href='${employee.resume}' target='_blank'>View Resume</a></div>` : ''}
              </div>
              <div class="mb-3">
                <label class="form-label">Status</label>
                <div class="toggle-switch">
                  <label style="width:100%;height:100%;margin-bottom:0;cursor:pointer;">
                    <input type="checkbox" id="statusToggle" ${employee.status === 'active' ? 'checked' : ''} />
                    <span class="slider"></span>
                  </label>
                  <span class="toggle-label" id="statusLabel">${employee.status === 'active' ? 'Active' : 'Inactive'}</span>
                  <input type="hidden" name="status" id="status" value="${employee.status === 'active' ? 'active' : 'inactive'}" />
                </div>
              </div>
              <button type="submit" class="btn btn-primary mt-4">${mode === 'add' ? 'Add' : 'Update'}</button>
            </form>
          `;

          const statusToggle = contentElement.querySelector('#statusToggle');
          const statusLabel = contentElement.querySelector('#statusLabel');
          const statusHidden = contentElement.querySelector('#status');
          function updateStatusUI() {
            if (statusToggle.checked) {
              statusLabel.textContent = 'Active';
              statusLabel.style.color = '#198754';
              statusHidden.value = 'active';
            } else {
              statusLabel.textContent = 'Inactive';
              statusLabel.style.color = '#dc3545';
              statusHidden.value = 'inactive';
            }
          }
          statusToggle.addEventListener('change', updateStatusUI);
          updateStatusUI();

          const nameInput = contentElement.querySelector('#empName');
          const nameError = contentElement.querySelector('#nameError');
          const phoneInput = contentElement.querySelector('#empPhone');
          const phoneError = contentElement.querySelector('#phoneError');
          const deptInput = contentElement.querySelector('#employeeDepartmentSelect');
          const deptError = contentElement.querySelector('#deptError');
          const designationInput = contentElement.querySelector('#empDesignation');
          const designationError = contentElement.querySelector('#designationError');
          const emailInput = contentElement.querySelector('input[name="email"]');
          let emailError = emailInput.nextElementSibling;
          if (!emailError || !emailError.id || emailError.id !== 'emailError') {
            emailError = document.createElement('div');
            emailError.id = 'emailError';
            emailError.className = 'text-danger small mt-1';
            emailError.style.display = 'none';
            emailInput.parentNode.appendChild(emailError);
          }
          const photoInput = contentElement.querySelector('#empPhoto');
          const photoError = contentElement.querySelector('#photoError');
          const resumeInput = contentElement.querySelector('#empResume');
          const resumeError = contentElement.querySelector('#resumeError');

          contentElement.querySelector('#employeeForm').onsubmit = function(e) {
            e.preventDefault();

            nameError.style.display = 'none'; nameError.textContent = '';
            phoneError.style.display = 'none'; phoneError.textContent = '';
            deptError.style.display = 'none'; deptError.textContent = '';
            designationError.style.display = 'none'; designationError.textContent = '';
            photoError.style.display = 'none'; photoError.textContent = '';
            resumeError.style.display = 'none'; resumeError.textContent = '';
            emailError.style.display = 'none'; emailError.textContent = '';

            let valid = true;
            const nameVal = nameInput.value.trim();
            nameInput.value = nameVal;
            if (!nameVal) {
              nameError.textContent = 'Name is required';
              nameError.style.display = 'block';
              valid = false;
            } else if (!/^[A-Za-z ]+$/.test(nameVal)) {
              nameError.textContent = 'Name must contain only letters and spaces';
              nameError.style.display = 'block';
              valid = false;
            } else if (/^\s|\s$/.test(nameInput.value) || /^\s|\s$/.test(nameVal)) {
              nameError.textContent = 'No leading or trailing spaces allowed';
              nameError.style.display = 'block';
              valid = false;
            }

            const emailVal = emailInput.value.trim();
            emailInput.value = emailVal;
            if (!emailVal) {
              emailError.textContent = 'Email is required';
              emailError.style.display = 'block';
              valid = false;
            } else if (!/^([a-zA-Z0-9_\.-]+)@([a-zA-Z0-9\.-]+)\.([a-zA-Z]{2,})$/.test(emailVal)) {
              emailError.textContent = 'Enter a valid email address';
              emailError.style.display = 'block';
              valid = false;
            }

            const phoneVal = phoneInput.value.trim();
            phoneInput.value = phoneVal;
            if (!/^[6-9][0-9]{9}$/.test(phoneVal)) {
              phoneError.textContent = 'Enter valid Indian mobile number (10 digits, starts with 6-9)';
              phoneError.style.display = 'block';
              valid = false;
            } else if (/^(\d)\1{9}$/.test(phoneVal)) {
              phoneError.textContent = 'All digits cannot be the same';
              phoneError.style.display = 'block';
              valid = false;
            }

            if (!deptInput.value) {
              deptError.textContent = 'Department is required';
              deptError.style.display = 'block';
              valid = false;
            }

            const designationVal = designationInput.value.trim();
            designationInput.value = designationVal;
            if (!designationVal) {
              designationError.textContent = 'Designation is required';
              designationError.style.display = 'block';
              valid = false;
            } else if (!/^[A-Za-z ]+$/.test(designationVal)) {
              designationError.textContent = 'Designation must contain only letters and spaces';
              designationError.style.display = 'block';
              valid = false;
            }

            if (photoInput.files.length > 0) {
              const file = photoInput.files[0];
              if (!/^image\/(jpeg|png|jpg|gif|webp)$/.test(file.type)) {
                photoError.textContent = 'Only image files allowed (jpeg, png, jpg, gif, webp)';
                photoError.style.display = 'block';
                valid = false;
              } else if (file.size > 2 * 1024 * 1024) {
                photoError.textContent = 'Photo must be less than 2MB';
                photoError.style.display = 'block';
                valid = false;
              }
            }

            if (resumeInput.files.length > 0) {
              const file = resumeInput.files[0];
              if (!/(pdf|msword|vnd.openxmlformats-officedocument.wordprocessingml.document)$/.test(file.type)) {
                resumeError.textContent = 'Only PDF, DOC, DOCX files allowed';
                resumeError.style.display = 'block';
                valid = false;
              } else if (file.size > 2 * 1024 * 1024) {
                resumeError.textContent = 'Resume must be less than 2MB';
                resumeError.style.display = 'block';
                valid = false;
              }
            }

            if (!valid) return;

            const formData = new FormData(e.target);
            if (mode === 'edit') {
              formData.set('_method', 'PUT');
            }
            formData.set('_token', csrfToken);
            fetch(url, {
              method: 'POST',
              headers: { 'X-CSRF-TOKEN': csrfToken, 'X-Requested-With': 'XMLHttpRequest' },
              body: formData
            }).then(res => {
              if (res.ok) {
                popup.hide();
                grid.refresh();
              } else if (res.status === 422) {
                res.json().then(data => {
                  if (data.errors) {
                    Object.keys(data.errors).forEach(function(key) {
                      const field = contentElement.querySelector(`[name="${key}"]`);
                      const errorDiv = document.createElement('div');
                      errorDiv.className = 'form-error text-danger mt-1';
                      errorDiv.innerText = data.errors[key][0];
                      if (field && field.parentNode) field.parentNode.appendChild(errorDiv);
                    });
                  } else {
                    alert(data.message || 'Validation error');
                  }
                });
              } else {
                res.json().then(data => alert(data.message || 'Error'));
              }
            });
          };
        },
        onHidden: function() {
          popup.dispose();
        },
        showCloseButton: true
      });
    }
  }

  function showDeletePopup(id) {
    const popup = new DevExpress.ui.dxPopup(document.getElementById('deleteConfirmPopup'), {
      title: 'Confirm Delete',
      visible: true,
      width: 350,
      height: 200,
      contentTemplate: function(contentElement) {
        contentElement.innerHTML = `
          <div class="text-center">
            <p>Are you sure you want to delete this employee?</p>
            <button id="confirmDeleteBtn" class="btn btn-danger me-2">Delete</button>
            <button id="cancelDeleteBtn" class="btn btn-secondary">Cancel</button>
          </div>
        `;
        contentElement.querySelector('#confirmDeleteBtn').onclick = function() {
          fetch(`/employees/${id}`, {
            method: 'DELETE',
            headers: { 'X-CSRF-TOKEN': csrfToken, 'X-Requested-With': 'XMLHttpRequest' }
          }).then(res => {
            popup.hide();
            grid.refresh();
          });
        };
        contentElement.querySelector('#cancelDeleteBtn').onclick = function() {
          popup.hide();
        };
      },
      onHidden: function() {
        popup.dispose();
      },
      showCloseButton: true
    });
  }
});
</script>
@endpush
