/**
 * Department Management jQuery Module
 * Handles department CRUD operations, validation, and UI interactions
 */

class DepartmentManager {
    constructor() {
        this.validator = new ValidationUtils();
        this.csrfToken = $('meta[name="csrf-token"]').attr('content');
        this.init();
    }

    init() {
        this.setupValidation();
        this.setupEventListeners();
        this.setupInputFormatting();
    }

    /**
     * Setup form validation rules
     */
    setupValidation() {
        const rules = {
            name: [
                { type: 'required' },
                { type: 'maxLength', value: 255 }
            ],
            code: [
                { type: 'required' },
                { type: 'alphanumeric' },
                { type: 'minLength', value: 2 },
                { type: 'maxLength', value: 10 }
            ],
            status: [
                { type: 'required' }
            ]
        };

        const messages = {
            name: {
                required: 'Department name is required.',
                maxLength: 'Name cannot exceed 255 characters.'
            },
            code: {
                required: 'Department code is required.',
                alphanumeric: 'Code can only contain letters and numbers (no special characters).',
                minLength: 'Code must be at least 2 characters.',
                maxLength: 'Code cannot exceed 10 characters.'
            },
            status: {
                required: 'Department status is required.'
            }
        };

        // Initialize validation for department forms
        this.validator.init('#departmentForm', rules, messages);
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        const self = this;

        // Form submission
        $(document).on('submit', '#departmentForm', function(e) {
            e.preventDefault();
            self.submitForm($(this));
        });

        // Delete confirmation with employee check
        $(document).on('click', '.delete-department', function(e) {
            e.preventDefault();
            const departmentId = $(this).data('id');
            const departmentName = $(this).data('name');
            self.checkEmployeesBeforeDelete(departmentId, departmentName);
        });

        // Restore department
        $(document).on('click', '.restore-department', function(e) {
            e.preventDefault();
            const departmentId = $(this).data('id');
            self.restoreDepartment(departmentId);
        });

        // Permanent delete
        $(document).on('click', '.permanent-delete-department', function(e) {
            e.preventDefault();
            const departmentId = $(this).data('id');
            const departmentName = $(this).data('name');
            self.confirmPermanentDelete(departmentId, departmentName);
        });
    }

    /**
     * Setup input formatting
     */
    setupInputFormatting() {
        // Format code field (alphanumeric only) - Enhanced validation
        $(document).on('input', '#code', function() {
            if (this.value) {
                // Remove any special characters, keep only letters and numbers
                this.value = this.value.replace(/[^a-zA-Z0-9]/g, '');
                // Convert to uppercase for consistency
                this.value = this.value.toUpperCase();
                // Limit to 10 characters
                if (this.value.length > 10) {
                    this.value = this.value.substring(0, 10);
                }
            }
        });

        // Clear errors on input with instant feedback
        $(document).on('input', 'input, select, textarea', function() {
            const field = $(this);
            if (field.hasClass('is-invalid')) {
                // Use the instance method through the validator instance
                if (window.departmentManager && window.departmentManager.validator) {
                    window.departmentManager.validator.clearFieldError(field);
                }
            }
        });

        // Add instant validation feedback for code field
        $(document).on('blur', '#code', function() {
            const codeValue = $(this).val();
            if (codeValue && codeValue.length > 0 && codeValue.length < 2) {
                // Use the instance method through the validator instance
                if (window.departmentManager && window.departmentManager.validator) {
                    window.departmentManager.validator.showFieldError($(this), 'Department code must be at least 2 characters.');
                }
            }
        });
    }

    /**
     * Submit department form
     */
    submitForm(form) {
        if (!this.validator.validateForm(form)) {
            ValidationUtils.showErrorMessage('Please fix the validation errors before submitting.');
            return;
        }

        const formData = new FormData(form[0]);
        const url = form.attr('action');
        const method = form.find('input[name="_method"]').val() || 'POST';

        $.ajax({
            url: url,
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    ValidationUtils.showSuccessMessage(
                        response.message || (method === 'PUT' ? 'Department updated successfully!' : 'Department created successfully!')
                    );

                    // Refresh grid if available
                    if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                        window.grid.refresh();
                    }

                    // Close modal or redirect
                    this.closeModal();
                } else {
                    ValidationUtils.showErrorMessage(response.message || 'An error occurred while saving the department.');
                }
            },
            error: (xhr) => {
                if (xhr.status === 422) {
                    // Validation errors
                    const errors = xhr.responseJSON?.errors;
                    if (errors) {
                        this.displayValidationErrors(errors);
                    } else {
                        const message = xhr.responseJSON?.message || 'Validation failed.';
                        ValidationUtils.showErrorMessage(message);
                    }
                } else {
                    const errorMessage = xhr.responseJSON?.message || 'An error occurred while saving the department.';
                    ValidationUtils.showErrorMessage(errorMessage);
                }
            }
        });
    }

    /**
     * Display validation errors from server
     */
    displayValidationErrors(errors) {
        $.each(errors, (field, messages) => {
            const fieldElement = $(`[name="${field}"]`);
            if (fieldElement.length) {
                this.validator.showFieldError(fieldElement, messages[0]);
            }
        });
    }

    /**
     * Check if department has employees before deletion
     */
    checkEmployeesBeforeDelete(departmentId, departmentName) {
        console.log('Checking employees for department:', departmentId, departmentName);

        $.ajax({
            url: `/departments/${departmentId}/check-employees`,
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                console.log('Employee check response:', response);

                if (response.success && response.hasEmployees) {
                    console.log('Department has employees, showing alert');
                    this.showEmployeeExistsAlert(response.totalEmployees, response.activeEmployees, departmentName);
                } else if (response.success) {
                    console.log('Department has no employees, proceeding with delete confirmation');
                    this.confirmDelete(departmentId, departmentName);
                } else {
                    console.error('Employee check failed:', response.message);
                    ValidationUtils.showErrorMessage(response.message || 'Failed to check department employees.');
                }
            },
            error: (xhr) => {
                console.error('Employee check AJAX error:', xhr);
                const errorMessage = xhr.responseJSON?.message || 'Failed to check department employees.';
                ValidationUtils.showErrorMessage(errorMessage);
                // Don't proceed with delete if check fails
            }
        });
    }

    /**
     * Show alert when department has employees
     */
    showEmployeeExistsAlert(totalEmployees, activeEmployees, departmentName) {
        const employeeText = totalEmployees === 1 ? 'employee' : 'employees';
        const activeText = activeEmployees > 0 ? ` (${activeEmployees} active)` : '';
        const message = `Cannot delete department "${departmentName}" because it has ${totalEmployees} ${employeeText} assigned to it${activeText}.\n\nPlease reassign or remove the employees first.`;

        // Show both alert and error message
        alert(message);
        ValidationUtils.showErrorMessage(`Cannot delete department "${departmentName}" - it has ${totalEmployees} ${employeeText} assigned.`);
    }

    /**
     * Confirm delete department using DevExtreme popup
     */
    confirmDelete(departmentId, departmentName) {
        // Remove any existing popup
        if (window.deleteDepartmentPopup) {
            window.deleteDepartmentPopup.dispose();
            delete window.deleteDepartmentPopup;
        }

        const popup = $("<div>").appendTo("body").dxPopup({
            title: "Delete Department",
            contentTemplate: function() {
                return `<div>Are you sure you want to delete department <strong>"${departmentName}"</strong>?</div>`;
            },
            showTitle: true,
            visible: true,
            width: 400,
            height: 'auto',
            dragEnabled: false,
            closeOnOutsideClick: true,
            showCloseButton: true,
            toolbarItems: [
                {
                    widget: 'dxButton',
                    toolbar: 'bottom',
                    location: 'after',
                    options: {
                        text: 'Cancel',
                        onClick: function() {
                            popup.dxPopup('instance').hide();
                        }
                    }
                },
                {
                    widget: 'dxButton',
                    toolbar: 'bottom',
                    location: 'after',
                    options: {
                        text: 'Delete',
                        type: 'danger',
                        onClick: () => {
                            popup.dxPopup('instance').hide();
                            this.deleteDepartment(departmentId);
                        }
                    }
                }
            ],
            onHidden: function() {
                popup.dxPopup('instance').dispose();
                popup.remove();
                if (window.deleteDepartmentPopup) delete window.deleteDepartmentPopup;
            }
        });
        window.deleteDepartmentPopup = popup;
    }

    /**
     * Delete department
     */
    deleteDepartment(departmentId) {
        $.ajax({
            url: `/departments/${departmentId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    ValidationUtils.showSuccessMessage(response.message || 'Department deleted successfully!');
                } else {
                    ValidationUtils.showErrorMessage(response.message || 'Failed to delete department.');
                }

                if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                    window.grid.refresh();
                }
            },
            error: (xhr) => {
                const errorMessage = xhr.responseJSON?.message || 'Failed to delete department.';
                ValidationUtils.showErrorMessage(errorMessage);
            }
        });
    }

    /**
     * Restore department
     */
    restoreDepartment(departmentId) {
        $.ajax({
            url: `/departments/${departmentId}/restore`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: (response) => {
                if (response.success) {
                    ValidationUtils.showSuccessMessage(response.message || 'Department restored successfully!');
                } else {
                    ValidationUtils.showErrorMessage(response.message || 'Failed to restore department.');
                }

                if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                    window.grid.refresh();
                }
            },
            error: (xhr) => {
                const errorMessage = xhr.responseJSON?.message || 'Failed to restore department.';
                ValidationUtils.showErrorMessage(errorMessage);
            }
        });
    }

    /**
     * Confirm permanent delete
     */
    confirmPermanentDelete(departmentId, departmentName) {
        const message = `Are you sure you want to permanently delete department "${departmentName}"?\n\nThis action cannot be undone!`;
        if (confirm(message)) {
            this.permanentDeleteDepartment(departmentId);
        }
    }

    /**
     * Permanently delete department
     */
    permanentDeleteDepartment(departmentId) {
        $.ajax({
            url: `/departments/${departmentId}/force-delete`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': this.csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: () => {
                ValidationUtils.showSuccessMessage('Department permanently deleted!');
                if (typeof window.grid !== 'undefined' && window.grid.refresh) {
                    window.grid.refresh();
                }
            },
            error: () => {
                ValidationUtils.showErrorMessage('Failed to permanently delete department.');
            }
        });
    }

    /**
     * Close modal or form
     */
    closeModal() {
        // Close DevExpress popup if exists
        if (typeof window.departmentPopup !== 'undefined') {
            window.departmentPopup.hide();
        }

        // Reset form
        $('#departmentForm')[0]?.reset();
        $('.is-invalid').removeClass('is-invalid');
        $('.invalid-feedback').remove();
    }

    /**
     * Filter departments based on search
     */
    filterDepartments() {
        if (typeof window.grid !== 'undefined' && window.grid.refresh) {
            window.grid.refresh();
        }
    }
}

// Initialize when document is ready (only if jQuery is available)
console.log('Department Management JS loaded');
console.log('jQuery available:', typeof $ !== 'undefined');

if (typeof $ !== 'undefined') {
    $(document).ready(function() {
        console.log('Initializing DepartmentManager with jQuery');
        window.departmentManager = new DepartmentManager();
        console.log('DepartmentManager initialized:', window.departmentManager);
    });
} else {
    console.log('jQuery not available, setting up fallback');
    // Fallback for when jQuery loads later
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM loaded, checking for jQuery again');
        if (typeof $ !== 'undefined') {
            $(document).ready(function() {
                console.log('Initializing DepartmentManager with jQuery (fallback)');
                window.departmentManager = new DepartmentManager();
                console.log('DepartmentManager initialized (fallback):', window.departmentManager);
            });
        } else {
            console.error('jQuery still not available after DOM load');
        }
    });
}
