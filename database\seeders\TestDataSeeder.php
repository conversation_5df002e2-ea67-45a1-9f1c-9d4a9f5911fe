<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Department;
use App\Models\Employee;

class TestDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user if none exists
        $user = User::first();
        if (!$user) {
            $user = User::factory()->create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
            ]);
        }

        // Create test departments
        $deptWithEmployees = Department::create([
            'name' => 'IT Department',
            'code' => 'IT',
            'status' => 'active',
            'added_admin' => $user->id,
            'updated_admin' => $user->id,
        ]);

        $deptWithoutEmployees = Department::create([
            'name' => 'Empty Department',
            'code' => 'EMPTY',
            'status' => 'active',
            'added_admin' => $user->id,
            'updated_admin' => $user->id,
        ]);

        // Create test employees in IT department
        Employee::create([
            'emp_id' => 'EMP001',
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'department_id' => $deptWithEmployees->id,
            'designation' => 'Software Developer',
            'status' => 'active',
            'added_admin' => $user->id,
            'updated_admin' => $user->id,
        ]);

        Employee::create([
            'emp_id' => 'EMP002',
            'name' => 'Jane Smith',
            'email' => '<EMAIL>',
            'phone' => '0987654321',
            'department_id' => $deptWithEmployees->id,
            'designation' => 'Project Manager',
            'status' => 'active',
            'added_admin' => $user->id,
            'updated_admin' => $user->id,
        ]);

        // Create a soft-deleted employee
        $deletedEmployee = Employee::create([
            'emp_id' => 'EMP003',
            'name' => 'Bob Johnson',
            'email' => '<EMAIL>',
            'phone' => '1122334455',
            'department_id' => $deptWithEmployees->id,
            'designation' => 'Tester',
            'status' => 'inactive',
            'added_admin' => $user->id,
            'updated_admin' => $user->id,
        ]);
        $deletedEmployee->delete(); // Soft delete

        echo "Test data created:\n";
        echo "- IT Department (ID: {$deptWithEmployees->id}) with 3 employees (1 soft-deleted)\n";
        echo "- Empty Department (ID: {$deptWithoutEmployees->id}) with 0 employees\n";
        echo "- User: {$user->name} ({$user->email})\n";
    }
}
