<?php $__env->startSection('content'); ?>
  <?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <?php echo e(session('success')); ?>

      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>
  <?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      <?php echo e(session('error')); ?>

      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>
            <i class="fas fa-trash me-2"></i>
            Trashed Departments
        </h3>
        <div>
            <a href="<?php echo e(route('departments.index')); ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>
                Back
            </a>
        </div>
    </div>

    <div id="departmentsTrashedGrid"></div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function () {
  const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

  const store = new DevExpress.data.CustomStore({
    key: 'id',
    load: function(loadOptions) {
      const params = new URLSearchParams();
      if (loadOptions.skip) params.append('skip', loadOptions.skip);
      if (loadOptions.take) params.append('take', loadOptions.take);

      return fetch('<?php echo e(route('departments.trashed.data')); ?>?' + params, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': csrfToken,
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
      })
      .then(json => {
        if (json.error) {
          throw new Error(json.message || 'Unknown error');
        }
        return { data: json.data, totalCount: json.totalCount };
      })
      .catch(error => {
        console.error('Error loading trashed departments:', error);
        // Show a more user-friendly message
        const errorMsg = error.message.includes('HTTP error') ?
          'Server error occurred. Please try again.' :
          error.message;
        alert('Error loading trashed departments: ' + errorMsg);
        return { data: [], totalCount: 0 };
      });
    }
  });

  new DevExpress.ui.dxDataGrid(document.getElementById('departmentsTrashedGrid'), {
    dataSource: store,
    remoteOperations: { paging: true, sorting: true, filtering: true },
    paging: { pageSize: 10 },
    showBorders: true,
    rowAlternationEnabled: true,
    columns: [
      { dataField: 'id', caption: 'ID', width: 80 },
      { dataField: 'name', caption: 'Department Name' },
      { dataField: 'code', caption: 'Code', width: 100 },
      {
        dataField: 'status',
        caption: 'Status',
        width: 100,
        cellTemplate: function(container, options) {
          const status = options.value;
          const badgeClass = status === 'active' ? 'badge bg-success' : 'badge bg-danger';
          container.innerHTML = `<span class="${badgeClass}">${status}</span>`;
        }
      },
      {
        dataField: 'deleted_at',
        caption: 'Deleted At',
        width: 150,
        cellTemplate: function(container, options) {
          container.innerHTML = `<small class="text-muted">${options.value}</small>`;
        }
      },
      {
        caption: 'Actions',
        width: 120,
        allowSorting: false,
        cellTemplate: function(container, options) {
          const id = options.data.id;
          const restoreBtn = `
            <form method="POST" action="/departments/${id}/restore" style="display:inline" onsubmit="return confirm('Are you sure you want to restore this department?')">
              <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
              <button type="submit" class="btn btn-sm btn-success" title="Restore Department">
                <i class="fas fa-undo me-1"></i>
                Restore
              </button>
            </form>`;
          container.innerHTML = restoreBtn;
        }
      }
    ],
    onRowPrepared: function(e) {
      if (e.rowType === 'data') {
        e.rowElement.style.opacity = '0.8';
      }
    }
  });
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .btn-outline-secondary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
    }

    .btn-success:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3);
    }

    .alert {
        border: none;
        border-radius: 8px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\employee-department-manage\resources\views/departments/trashed.blade.php ENDPATH**/ ?>