<?php $__env->startSection('content'); ?>
<div class="container">
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h3>
      <i class="fas fa-building me-2"></i>
      Departments
    </h3>
    <div>
      <button id="TrashView" class="btn btn-outline-secondary me-2">
        <i class="fas fa-trash me-1"></i>
        <span id="trashName">View Trashed</span>
      </button>
      <button id="addDepartmentBtn" class="btn btn-primary">
        <i class="fas fa-plus me-1"></i>
        Add Department
      </button>
    </div>
  </div>

  <?php if(session('success')): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      <?php echo e(session('success')); ?>

      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>
  <?php if(session('error')): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      <?php echo e(session('error')); ?>

      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  <?php endif; ?>

  <div id="departmentsLists"></div>
  <div id="departmentCreateEdit"></div>
  <div id="deleteConfirmPopup"></div>
</div>
<?php $__env->stopSection(); ?>


<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function () {
  const csrfToken = "<?php echo e(csrf_token()); ?>";
  let isTrash = false;

  // Function to show dynamic messages
  function showMessage(message, type = 'success') {
    // Remove existing alerts
    const existingAlerts = document.querySelectorAll('.alert');
    existingAlerts.forEach(alert => alert.remove());

    // Create new alert
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;

    // Insert at the top of the container
    const container = document.querySelector('.container');
    container.insertBefore(alertDiv, container.firstChild);

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
      if (alertDiv && alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }

  function getStoreUrl() {
    return isTrash ? '<?php echo e(route('departments.trashed.data')); ?>' : '<?php echo e(route('departments.data')); ?>';
  }

  const store = new DevExpress.data.CustomStore({
    key: 'id',
    load: function(loadOptions) {
      const params = new URLSearchParams();
      if (loadOptions.skip) params.append('skip', loadOptions.skip);
      if (loadOptions.take) params.append('take', loadOptions.take);
      return fetch(getStoreUrl() + '?' + params, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': csrfToken,
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => response.json())
      .then(json => {
        if (json.error) throw new Error(json.message || 'Unknown error');
        return { data: json.data, totalCount: json.totalCount };
      })
      .catch(error => {
        alert('Error loading departments: ' + error.message);
        return { data: [], totalCount: 0 };
      });
    }
  });

  const grid = new DevExpress.ui.dxDataGrid(document.getElementById('departmentsLists'), {
    dataSource: store,
    remoteOperations: { paging: true, sorting: true, filtering: true },
    paging: { pageSize: 10 },
    showBorders: true,
    rowAlternationEnabled: true,
    columns: [
      { dataField: 'id', caption: 'ID', width: 80 },
      { dataField: 'name', caption: 'Department Name' },
      { dataField: 'code', caption: 'Code', width: 100 },
      {
        dataField: 'status',
        caption: 'Status',
        width: 100,
        cellTemplate: function(container, options) {
          const status = options.value;
          const badgeClass = status === 'active' ? 'badge bg-success' : 'badge bg-danger';
          container.innerHTML = `<span class="${badgeClass}">${status}</span>`;
        }
      },
      {
        caption: 'Actions',
        width: 180,
        allowSorting: false,
        cellTemplate: function(container, options) {
          const id = options.data.id;
          let btns = '';
          if (!isTrash) {
            btns += `<button class='btn btn-sm btn-outline-primary me-1' onclick='editDepartment(${id})'><i class="fas fa-edit"></i></button>`;
            btns += `<button class='btn btn-sm btn-outline-danger' onclick='confirmDelete(${id})'><i class="fas fa-trash"></i></button>`;
          } else {
            btns += `<button class='btn btn-sm btn-outline-success' onclick='restoreDepartment(${id})'><i class="fas fa-undo"></i> Restore</button>`;
          }
          container.innerHTML = btns;
        }
      }
    ],
    onRowPrepared: function(e) {
      if (e.rowType === 'data' && e.data.status === 'inactive') {
        e.rowElement.style.opacity = '0.6';
      }
    }
  });

  window.editDepartment = function(id) {
    showDepartmentCreateEdit('edit', id);
  };
  window.confirmDelete = function(id) {
    showDeletePopup(id);
  };
  window.restoreDepartment = function(id) {
    fetch(`/departments/${id}/restore`, {
      method: 'POST',
      headers: { 'X-CSRF-TOKEN': csrfToken, 'X-Requested-With': 'XMLHttpRequest' }
    })
    .then(response => response.json())
    .then(data => {
      if (data.success) {
        showMessage(data.message, 'success');
      } else {
        showMessage(data.message || 'Failed to restore department', 'error');
      }
      grid.refresh();
    })
    .catch(error => {
      showMessage('Error restoring department: ' + error.message, 'error');
      grid.refresh();
    });
  };

  document.getElementById('addDepartmentBtn').addEventListener('click', function() {
    showDepartmentCreateEdit('add');
  });
  document.getElementById('TrashView').addEventListener('click', function() {
    isTrash = !isTrash;
    document.getElementById('trashName').innerText = isTrash ? 'View Active' : 'View Trashed';
    grid.refresh();
  });

  function showDepartmentCreateEdit(mode, id = null) {
  let title = mode === 'add' ? 'Add Department' : 'Edit Department';
  let url = mode === 'add' ? '/departments' : `/departments/${id}`;
  let department = { name: '', code: '', status: 'active' };

    if (mode === 'edit') {
      fetch(`/departments/${id}/editdata`, { headers: { 'X-Requested-With': 'XMLHttpRequest' } })
        .then(res => res.json())
        .then(data => {
          department = data;
          openPopup();
        });
    } else {
      openPopup();
    }

    function openPopup() {
      const popup = new DevExpress.ui.dxPopup(document.getElementById('departmentCreateEdit'), {
        title: title,
        visible: true,
        width: 1000,
        height: 400,
        contentTemplate: function(contentElement) {
          contentElement.innerHTML = `
            <style>
              .toggle-switch {
                position: relative;
                display: inline-block;
                width: 60px;
                height: 32px;
                vertical-align: middle;
              }
              .toggle-switch input[type="checkbox"] {
                opacity: 0;
                width: 0;
                height: 0;
              }
              .slider {
                position: absolute;
                cursor: pointer;
                top: 0; left: 0; right: 0; bottom: 0;
                background-color: #dc3545;
                transition: .4s;
                border-radius: 32px;
              }
              .slider:before {
                position: absolute;
                content: "";
                height: 24px;
                width: 24px;
                left: 4px;
                bottom: 4px;
                background-color: white;
                transition: .4s;
                border-radius: 50%;
              }
              input[type="checkbox"]:checked + .slider {
                background-color: #198754;
              }
              input[type="checkbox"]:checked + .slider:before {
                transform: translateX(28px);
              }
              .toggle-label {
                margin-left: 12px;
                font-weight: bold;
                vertical-align: middle;
                color: #dc3545;
                transition: color .4s;
                cursor: pointer;
              }
              .toggle-switch input[type="checkbox"]:checked ~ .toggle-label {
                color: #198754;
              }
            </style>
            <form id="departmentForm" autoComplete="off">
              ${mode === 'edit' ? '<input type="hidden" name="_method" value="PUT" />' : ''}
              <div class="mb-3">
                <label class="form-label">Name</label>
                <input type="text" class="form-control" placeholder="Name" name="name" id="name" value="${department.name || ''}" />
                <div id="nameError" class="text-danger small mt-1" style="display:none;"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Code</label>
                <input type="text" class="form-control" name="code" id="code" maxlength="10" minlength="2" placeholder="Code" value="${department.code || ''}" />
                <div id="codeError" class="text-danger small mt-1" style="display:none;"></div>
              </div>
              <div class="mb-3">
                <label class="form-label">Status</label>
                <div class="toggle-switch">
                  <label style="width:100%;height:100%;margin-bottom:0;cursor:pointer;">
                    <input type="checkbox" id="statusToggle" ${department.status === 'active' ? 'checked' : ''} />
                    <span class="slider"></span>
                  </label>
                  <span class="toggle-label" id="statusLabel">${department.status === 'active' ? 'Active' : 'Inactive'}</span>
                  <input type="hidden" name="status" id="status" value="${department.status === 'active' ? 'active' : 'inactive'}" />
                </div>
              </div>
              <button type="submit" class="btn btn-primary mt-4">${mode === 'add' ? 'Add' : 'Update'}</button>
            </form>
          `;

          const nameInput = contentElement.querySelector('#name');
          const nameError = contentElement.querySelector('#nameError');
          const codeInput = contentElement.querySelector('#code');
          const codeError = contentElement.querySelector('#codeError');

          nameError.style.display = 'none';
          nameError.textContent = '';
          codeError.style.display = 'none';
          codeError.textContent = '';

          let triedSubmit = false;
          function validateNameInput() {
            const val = nameInput.value;
            if (!val && triedSubmit) {
              nameError.textContent = 'Name is required';
              nameError.style.display = 'block';
            } else if (val && !/^[A-Za-z\s]+$/.test(val)) {
              nameError.textContent = 'Name must contain only letters (A-Z, a-z)';
              nameError.style.display = 'block';
            } else {
              nameError.style.display = 'none';
              nameError.textContent = '';
            }
          }
          function validateCodeInput() {
            const val = codeInput.value;
            if (!val && triedSubmit) {
              codeError.textContent = 'Code is required';
              codeError.style.display = 'block';
            } else if (val && !/^[A-Za-z\s]+$/.test(val)) {
              codeError.textContent = 'Code must contain only letters (A-Z, a-z)';
              codeError.style.display = 'block';
            } else {
              codeError.style.display = 'none';
              codeError.textContent = '';
            }
          }
          nameInput.addEventListener('input', validateNameInput);
          codeInput.addEventListener('input', validateCodeInput);

          const statusToggle = contentElement.querySelector('#statusToggle');
          const statusLabel = contentElement.querySelector('#statusLabel');
          const statusHidden = contentElement.querySelector('#status');

          statusLabel.style.color = statusHidden.value === 'active' ? '#198754' : '#dc3545';
          statusLabel.textContent = statusHidden.value === 'active' ? 'Active' : 'Inactive';
          function updateStatusUI() {
            if (statusToggle.checked) {
              statusLabel.textContent = 'Active';
              statusLabel.style.color = '#198754';
              statusHidden.value = 'active';
            } else {
              statusLabel.textContent = 'Inactive';
              statusLabel.style.color = '#dc3545';
              statusHidden.value = 'inactive';
            }
          }
          statusToggle.addEventListener('change', updateStatusUI);
          updateStatusUI();

          contentElement.querySelector('#departmentForm').onsubmit = function(e) {
            e.preventDefault();
            triedSubmit = true;

            const nameVal = nameInput.value.trim();
            nameInput.value = nameVal;
            const codeVal = codeInput.value.trim();
            codeInput.value = codeVal;

            let valid = true;
            if (!nameVal) {
              nameError.textContent = 'Name is required';
              nameError.style.display = 'block';
              nameInput.focus();
              valid = false;
            } else if (!/^[A-Za-z\s]+$/.test(nameVal)) {
              nameError.textContent = 'Name must contain only letters (A-Z, a-z)';
              nameError.style.display = 'block';
              nameInput.focus();
              valid = false;
            } else {
              nameError.style.display = 'none';
              nameError.textContent = '';
            }
            if (!codeVal) {
              codeError.textContent = 'Code is required';
              codeError.style.display = 'block';
              if (valid) codeInput.focus();
              valid = false;
            } else if (!/^[A-Za-z\s]+$/.test(codeVal)) {
              codeError.textContent = 'Code must contain only letters (A-Z, a-z)';
              codeError.style.display = 'block';
              if (valid) codeInput.focus();
              valid = false;
            } else {
              codeError.style.display = 'none';
              codeError.textContent = '';
            }

            if (!statusHidden.value) {
              statusLabel.textContent = 'Status is required';
              statusLabel.style.color = '#dc3545';
              valid = false;
            }
            if (!valid) return;

            const formData = new FormData(e.target);
            formData.set('name', nameVal);
            formData.set('code', codeVal);
            formData.set('status', statusHidden.value);
            if (mode === 'edit') {
              formData.set('_method', 'PUT');
            }
            fetch(url, {
              method: 'POST',
              headers: { 'X-CSRF-TOKEN': csrfToken, 'X-Requested-With': 'XMLHttpRequest' },
              body: formData
            }).then(res => res.json())
            .then(data => {
              if (data.success) {
                showMessage(data.message, 'success');
                popup.hide();
                grid.refresh();
              } else if (data.errors) {
                // Handle validation errors
                if (data.errors.name) {
                  nameError.textContent = data.errors.name[0];
                  nameError.style.display = 'block';
                }
                if (data.errors.code) {
                  codeError.textContent = data.errors.code[0];
                  codeError.style.display = 'block';
                }
                if (data.errors.status) {
                  statusLabel.textContent = data.errors.status[0];
                  statusLabel.style.color = '#dc3545';
                }
              } else {
                showMessage(data.message || 'An error occurred', 'error');
              }
            })
            .catch(error => {
              showMessage('Network error: ' + error.message, 'error');
            });
          };
        },
        onHidden: function() {
          popup.dispose();
        },
        showCloseButton: true
      });
    }
  }

  function showDeletePopup(id) {
    const popup = new DevExpress.ui.dxPopup(document.getElementById('deleteConfirmPopup'), {
      title: 'Confirm Delete',
      visible: true,
      width: 350,
      height: 200,
      contentTemplate: function(contentElement) {
        contentElement.innerHTML = `
          <div class="text-center">
            <p>Are you sure you want to delete this department?</p>
            <button id="confirmDeleteBtn" class="btn btn-danger me-2">Delete</button>
            <button id="cancelDeleteBtn" class="btn btn-secondary">Cancel</button>
          </div>
        `;
        contentElement.querySelector('#confirmDeleteBtn').onclick = function() {
          if (!id) {
            showMessage('Error: Department ID is missing.', 'error');
            return;
          }
          fetch(`/departments/${id}`, {
            method: 'DELETE',
            headers: { 'X-CSRF-TOKEN': csrfToken, 'X-Requested-With': 'XMLHttpRequest' }
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              showMessage(data.message, 'success');
            } else {
              showMessage(data.message || 'Failed to delete department', 'error');
            }
            popup.hide();
            grid.refresh();
          })
          .catch(error => {
            showMessage('Error deleting department: ' + error.message, 'error');
            popup.hide();
            grid.refresh();
          });
        };
        contentElement.querySelector('#cancelDeleteBtn').onclick = function() {
          popup.hide();
        };
      },
      onHidden: function() {
        popup.dispose();
      },
      showCloseButton: true
    });
  }
});
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\employee-department-manage\resources\views/departments/index.blade.php ENDPATH**/ ?>