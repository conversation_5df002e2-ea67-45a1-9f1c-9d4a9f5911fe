@extends('layouts.app')

@section('content')
<div class="container">
    <h3>JavaScript Test Page</h3>
    
    <div class="row">
        <div class="col-md-6">
            <h5>Test Success Message</h5>
            <button id="testSuccess" class="btn btn-success">Show Success Message</button>
        </div>
        <div class="col-md-6">
            <h5>Test Error Message</h5>
            <button id="testError" class="btn btn-danger">Show Error Message</button>
        </div>
    </div>
    
    <div class="row mt-3">
        <div class="col-md-12">
            <h5>Test Department Manager</h5>
            <button id="testDeptManager" class="btn btn-info">Test Department Manager</button>
            <div id="testResult" class="mt-2"></div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="{{ asset('js/validation-utils.js') }}"></script>
<script src="{{ asset('js/message-utils.js') }}"></script>
<script src="{{ asset('js/department-management.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded');
    console.log('jQuery available:', typeof $ !== 'undefined');
    console.log('ValidationUtils available:', typeof ValidationUtils !== 'undefined');
    console.log('DepartmentManager available:', typeof window.departmentManager !== 'undefined');
    
    document.getElementById('testSuccess').addEventListener('click', function() {
        if (typeof ValidationUtils !== 'undefined') {
            ValidationUtils.showSuccessMessage('This is a test success message!');
        } else {
            alert('ValidationUtils not available');
        }
    });
    
    document.getElementById('testError').addEventListener('click', function() {
        if (typeof ValidationUtils !== 'undefined') {
            ValidationUtils.showErrorMessage('This is a test error message!');
        } else {
            alert('ValidationUtils not available');
        }
    });
    
    document.getElementById('testDeptManager').addEventListener('click', function() {
        const result = document.getElementById('testResult');
        result.innerHTML = `
            <p>jQuery: ${typeof $ !== 'undefined' ? '✅ Available' : '❌ Not Available'}</p>
            <p>ValidationUtils: ${typeof ValidationUtils !== 'undefined' ? '✅ Available' : '❌ Not Available'}</p>
            <p>DepartmentManager: ${typeof window.departmentManager !== 'undefined' ? '✅ Available' : '❌ Not Available'}</p>
            <p>DepartmentManager instance: ${window.departmentManager ? '✅ Initialized' : '❌ Not Initialized'}</p>
        `;
    });
});
</script>
@endpush
