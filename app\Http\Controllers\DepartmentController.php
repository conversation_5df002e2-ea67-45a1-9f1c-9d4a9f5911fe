<?php

namespace App\Http\Controllers;

use App\Models\Department;
use Illuminate\Http\Request;
use App\Http\Requests\DepartmentRequest;

class DepartmentController extends Controller
{

    public function index()
    {
        //$proName = config("constants.PROJECT_NAME");
        return view('departments.index');
    }

    public function data(Request $request)
    {
        try {
            $query = Department::with(['addedBy', 'updatedBy']);

            if ($search = $request->get('search')) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            }

            $total = $query->count();
            $skip = (int) $request->get('skip', 0);
            $take = (int) $request->get('take', 10);

            $items = $query->orderBy('id', 'desc')
                ->skip($skip)->take($take)->get();

            $items->transform(function ($item) {
                $item->added_by_name = $item->addedBy ? $item->addedBy->name : 'N/A';
                $item->updated_by_name = $item->updatedBy ? $item->updatedBy->name : 'N/A';
                return $item;
            });
            //pr($items->toArray());
            return response()->json([
                'data' => $items,
                'totalCount' => $total,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load departments',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function create()
    {
        //echo "Sam";die;
        return view('departments.create');
    }

    public function store(DepartmentRequest $request)
    {
        try {
            $validated = $request->validated();
            $departmentData = [
                'name' => $validated['name'],
                'code' => $validated['code'],
                'status' => $validated['status'],
            ];
            //pr($departmentData);
            Department::create($departmentData);

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Department created successfully!'
                ]);
            }

            return redirect()->route('departments.index')->with('success', 'Department created successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to create department: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('departments.index')->with('error', 'Failed to create department: ' . $e->getMessage());
        }
    }

    public function store_copy(DepartmentRequest $request)
    {
        //pr($request->all());
        Department::create($request->validated());
        return redirect()->route('departments.index')->with('success', 'Department created');
    }

    public function edit(Department $department)
    {
        //pr($department->toArray());
        return view('departments.edit', compact('department'));
    }

    public function showEditData(Department $department)
    {
        try {
            return response()->json($department);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load department data',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function update(DepartmentRequest $request, Department $department)
    {
        try {
            //pr($department->toArray());
            $department->update($request->validated());

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Department updated successfully!'
                ]);
            }

            return redirect()->route('departments.index')->with('success', 'Department updated successfully!');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to update department: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('departments.index')->with('error', 'Failed to update department: ' . $e->getMessage());
        }
    }

    public function destroy(Department $department)
    {
        try {
            // Check if department has employees
            $employeeCount = $department->employees()->count();

            if ($employeeCount > 0) {
                $errorMessage = "Cannot delete department '{$department->name}' because it has {$employeeCount} employee(s) assigned to it. Please reassign or remove the employees first.";

                if (request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => $errorMessage
                    ], 422);
                }

                return redirect()->route('departments.index')->with('error', $errorMessage);
            }

            //pr($department->toArray());
            $department->delete();
            //last_query();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Department deleted successfully!'
                ]);
            }

            return redirect()->route('departments.index')->with('success', 'Department deleted successfully!');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete department: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('departments.index')->with('error', 'Failed to delete department: ' . $e->getMessage());
        }
    }

    public function trashed()
    {
        return view('departments.trashed');
    }

    public function trashedData(Request $request)
    {
        try {
            $query = Department::onlyTrashed();

            $skip = $request->get('skip', 0);
            $take = $request->get('take', 10);

            $total = $query->count();
            $items = $query->skip($skip)->take($take)->get()->map(function ($department) {
                return [
                    'id' => $department->id,
                    'name' => $department->name ?? 'N/A',
                    'code' => $department->code ?? 'N/A',
                    'status' => $department->status ?? 'inactive',
                    'deleted_at' => $department->deleted_at ? $department->deleted_at->format('M d, Y H:i A') : 'N/A',
                ];
            });
            //pr($items->toArray());
            return response()->json([
                'data' => $items,
                'totalCount' => $total,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to load trashed departments: ' . $e->getMessage()
            ], 500);
        }
    }

    public function restore($id)
    {
        try {
            $department = Department::withTrashed()->findOrFail($id);
            $department->restore();

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Department restored successfully!'
                ]);
            }

            return redirect()->route('departments.trashed')->with('success', 'Department restored successfully!');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to restore department: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->route('departments.trashed')->with('error', 'Failed to restore department: ' . $e->getMessage());
        }
    }
}
