@extends('layouts.app')

@section('content')

  @if (session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
      {{ session('success') }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif
  @if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
      {{ session('error') }}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
  @endif

    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3>
            <i class="fas fa-trash me-2"></i>
            Trashed Employees
        </h3>
        <div>
            <a href="{{ route('employees.index') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>
                Back
            </a>
        </div>
    </div>

    <div id="employeesTrashedGrid"></div>

@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function () {
  const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

  const trashedDataUrl = "{{ route('employees.trashed.data') }}";
  const store = new DevExpress.data.CustomStore({
    key: 'id',
    load: function(loadOptions) {
      const params = new URLSearchParams();
      if (loadOptions.skip) params.append('skip', loadOptions.skip);
      if (loadOptions.take) params.append('take', loadOptions.take);

  return fetch(trashedDataUrl + '?' + params, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': csrfToken,
          'X-Requested-With': 'XMLHttpRequest'
        }
      })
      .then(response => {
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        return response.json();
      })
      .then(json => {
        if (json.error) throw new Error(json.error || 'Unknown error');
        return { data: json.data, totalCount: json.totalCount };
      })
      .catch(error => {
        console.error('Error loading trashed employees:', error);
        alert('Error loading trashed employees: ' + error.message);
        return { data: [], totalCount: 0 };
      });
    }
  });

  new DevExpress.ui.dxDataGrid(document.getElementById('employeesTrashedGrid'), {
    dataSource: store,
    remoteOperations: { paging: true, sorting: true, filtering: true },
    paging: { pageSize: 10 },
    showBorders: true,
    rowAlternationEnabled: true,
    columns: [
      { dataField: 'id', caption: 'ID', width: 70 },
      { dataField: 'emp_id', caption: 'Employee ID', width: 120 },
      {
        dataField: 'name',
        caption: 'Employee Name',
        cellTemplate: function(container, options) {
          const employee = options.data;

          container.innerHTML = `<div class="d-flex align-items-center">${employee.name}</div>`;
        }
      },
      { dataField: 'email', caption: 'Email' },
      { dataField: 'phone', caption: 'Phone', width: 120 },
      { dataField: 'department_name', caption: 'Department' },
      { dataField: 'designation', caption: 'Designation' },
      {
        dataField: 'status',
        caption: 'Status',
        width: 100,
        cellTemplate: function(container, options) {
          const status = options.value;
          const badgeClass = status === 'active' ? 'badge bg-success' : 'badge bg-danger';
          container.innerHTML = `<span class="${badgeClass}">${status}</span>`;
        }
      },
      {
        dataField: 'deleted_at',
        caption: 'Deleted At',
        width: 150,
        cellTemplate: function(container, options) {
          container.innerHTML = `<small class="text-muted">${options.value}</small>`;
        }
      },
      {
        caption: 'Actions',
        width: 120,
        allowSorting: false,
        cellTemplate: function(container, options) {
          const id = options.data.id;
          const restoreBtn = `
            <form method="POST" action="/employees/${id}/restore" style="display:inline" onsubmit="return confirm('Are you sure you want to restore this employee?')">
              <input type="hidden" name="_token" value="${csrfToken}">
              <button type="submit" class="btn btn-sm btn-success" title="Restore Employee">
                <i class="fas fa-undo me-1"></i>
                Restore
              </button>
            </form>`;
          container.innerHTML = restoreBtn;
        }
      }
    ],
    onRowPrepared: function(e) {
      if (e.rowType === 'data') {
        e.rowElement.style.opacity = '0.9';
      }
    }
  });
});
</script>
@endpush
